"use client";

"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "BailoutToCSR", {
    enumerable: true,
    get: function() {
        return BailoutToCSR;
    }
});
const _bailouttocsr = require("./bailout-to-csr");
function BailoutToCSR(param) {
    let { reason, children } = param;
    if (typeof window === "undefined") {
        throw new _bailouttocsr.BailoutToCSRError(reason);
    }
    return children;
}

//# sourceMappingURL=dynamic-bailout-to-csr.js.map